import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, StatusFilter, Variant } from '@/components/globals';
import { Button, Label, TextInput } from 'flowbite-react';
import DataGridView, { Column } from '@/components/globals/sortable-table/data-grid-view';
import useDebounce from '@/components/hooks/useDebounce';
import moment from 'moment';
import { useMemo, useState } from 'react';
import { FaPlus } from 'react-icons/fa6';
import ExportCSV4 from '@/components/globals/export-csv-4/export-csv';
import { HiSearch } from 'react-icons/hi';
import { AccountDetailsModal } from '../components/acount-details-modal';
import { AccountData, mockAccounts } from '@/lib/mock/accounts-data';
import { AddAccountModal } from './add-account-modal/add-account-modal';

export enum StatusEnum {
  active = 'active',
  processing = 'processing',
  draft = 'draft',
  submitted = 'submitted',
  inactive = 'inactive',
  reject = 'reject',
  pending = 'pending',
  approved = 'approved',
  sent = 'sent',
}

export const AccountsTab = () => {
  // Selected account data
  const [selectedAccount, setSelectedAccount] = useState<AccountData | null>(null);
  const [accountDetailsModal, setAccountDetailsModal] = useState(false);
  const [addAccountModal, setAddAccountModal] = useState(false);

  const [searchValue, setSearchValue] = useState('');
  const debouncedSearchQuery = useDebounce(searchValue, 500);
  const [pageSize, setPageSize] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState('All');
  const [isSortAsc, setIsSortAsc] = useState(false);
  const [sortKey, setSortKey] = useState('name');

  // Filter and paginate mock accounts
  const filteredAccounts = useMemo(() => {
    return mockAccounts
      .filter((account) => {
        const matchesSearch =
          !debouncedSearchQuery ||
          account.name.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
          account.email.toLowerCase().includes(debouncedSearchQuery.toLowerCase());
        const matchesStatus = statusFilter === 'All' || account.status === statusFilter;
        return matchesSearch && matchesStatus;
      })
      .sort((a, b) => {
        const compareResult = isSortAsc
          ? a[sortKey] > b[sortKey]
            ? 1
            : -1
          : a[sortKey] < b[sortKey]
            ? 1
            : -1;
        return compareResult;
      });
  }, [mockAccounts, debouncedSearchQuery, statusFilter, sortKey, isSortAsc]);

  // Get paginated data
  const paginatedAccounts = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return filteredAccounts.slice(startIndex, endIndex);
  }, [filteredAccounts, currentPage, pageSize]);

  const handleAddNewAccount = () => {
    setAddAccountModal(true);
  };

  const fetchExportData = async () => {
    return mockAccounts; // Return mock data for export
  };

  const handleApprove = (account: AccountData) => {
    // TODO: Implement approve functionality
    console.log('Approve account:', account.id);
  };

  const handleReject = (account: AccountData) => {
    // TODO: Implement reject functionality
    console.log('Reject account:', account.id);
  };

  const handleEdit = (account: AccountData) => {
    // TODO: Implement edit functionality
    console.log('Edit account:', account.id);
  };

  const handleDelete = (account: AccountData) => {
    // TODO: Implement delete functionality
    console.log('Delete account:', account.id);
  };

  const columns: Column<AccountData>[] = [
    {
      key: 'name',
      header: 'Name',
      renderCell: (row) => (
        <div className="flex flex-col">
          <span className="font-medium">{row.name}</span>
          <span className="text-sm text-gray-500">{row.accountId}</span>
        </div>
      ),
      onClick: (row) => {
        setSelectedAccount(row);
        setAccountDetailsModal(true);
      },
      sortable: true,
    },
    {
      key: 'id',
      header: 'Merchant ID',
      sortable: true,
      renderCell: (row) => <span>{row.id.slice(-9)}</span>,
    },
    {
      key: 'status',
      header: 'Status',
      renderCell: (row) => {
        const status = row.status;
        let color: Variant = 'neutral';

        switch (status) {
          case 'inactive':
          case 'reject':
            color = 'danger';
            break;
          case 'pending':
          case 'submitted':
          case 'sent':
            color = 'warning';
            break;
          case 'active':
          case 'approved':
            color = 'success';
            break;
          default:
            color = 'neutral';
        }

        return (
          <div
            className="cursor-pointer"
            onClick={() => {
              setSelectedAccount(row);
              // Show account details modal for all statuses
              setAccountDetailsModal(true);
            }}
          >
            <StatusChip variant={color} label={status} />
          </div>
        );
      },
      sortable: true,
    },
    {
      key: 'lastActive',
      header: 'Date added',
      valueGetter: (row) => moment(row.lastActive).format('MM/DD/YYYY'),
      sortable: true,
    },
    {
      key: 'partnerName',
      header: 'Partner',
      sortable: true,
    },

    {
      key: 'totalEarnings',
      header: 'Total Earnings',
      valueGetter: (row) =>
        ['sent', 'draft', 'reject'].includes(row.status)
          ? '-'
          : `$${row.totalEarnings.toLocaleString()}`,
      sortable: true,
    },
  ];

  return (
    <>
      <div className="items-bottom flex justify-between">
        <PageHeader text="Account" />
        <Button className="mt-[20px] h-[38px] p-0" color="blue" onClick={handleAddNewAccount}>
          <div className="flex items-center gap-x-3">
            <FaPlus className="text-xl" />
            <span>Add Account</span>
          </div>
        </Button>
      </div>
      <div className="hidden items-center border-b border-gray-300 py-2 dark:divide-gray-700 sm:mb-0 sm:flex sm:justify-between sm:divide-x sm:divide-gray-100">
        <form className="hidden lg:block">
          <div className="flex">
            <Label htmlFor="search" className="sr-only">
              Search
            </Label>
            <TextInput
              className="w-full rounded-br-none rounded-tr-none"
              icon={HiSearch}
              id="search"
              name="search"
              placeholder="Search"
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              type="search"
            />
            <Button color="blue" className="-ml-2 rounded-l-lg rounded-bl-none rounded-tl-none">
              Search
            </Button>
          </div>
        </form>
        <div className="flex gap-2">
          <div className="flex items-center space-x-2 sm:space-x-3">
            <ExportCSV4 columns={columns} dataFetcher={fetchExportData} reportName="accounts" />
          </div>
        </div>
      </div>

      <div>
        <DataGridView
          columns={columns}
          rows={paginatedAccounts}
          pageSize={pageSize}
          currentPage={currentPage}
          isLoading={false}
          actionComponent={
            <StatusFilter
              value={statusFilter}
              setValue={setStatusFilter}
              statusList={Object.values(StatusEnum)}
            />
          }
          mode="client"
          onPageChange={setCurrentPage}
          onPageSizeChange={setPageSize}
          totalRecords={filteredAccounts.length}
        />
      </div>
      {selectedAccount && (
        <>
          <AccountDetailsModal
            isOpen={accountDetailsModal}
            onClose={() => setAccountDetailsModal(false)}
            accountData={{
              id: selectedAccount.id,
              groupID: `GRP-${selectedAccount.id}`,
              friendlyName: selectedAccount.name,
              accountStatus: selectedAccount.status,
              dateAdded: selectedAccount.lastActive,
              paymentType: selectedAccount.status,
              dbaName: selectedAccount.name,
              email: selectedAccount.email,
              phone: selectedAccount.phoneNumber,
              country: 'United States',
              billingAddress: '123 Main Street, Suite 100, City, ST 12345',
              metrics: {
                monthlySales: Math.round(selectedAccount.totalEarnings / 12),
                closedTransactions: Math.round(selectedAccount.totalEarnings / 100),
                availableBalance: Math.round(selectedAccount.totalEarnings * 0.15),
              },
              limits: {
                cardPerTransaction: 5000,
                cardMonthly: 50000,
                cardMonthlyPercentage: 45,
                bankTransferPerTransaction: 10000,
                bankTransferMonthly: 100000,
                bankTransferMonthlyPercentage: 25,
              },
              bankInfo: {
                bankName: 'JPMORGAN CHASE BANK, NA',
                accountLast4: '4321',
                bankCode: '*********',
              },
            }}
            onApprove={() => handleApprove(selectedAccount)}
            onReject={() => handleReject(selectedAccount)}
            onEdit={() => handleEdit(selectedAccount)}
            onDelete={() => handleDelete(selectedAccount)}
          />
        </>
      )}

      <AddAccountModal isOpen={addAccountModal} onClose={() => setAddAccountModal(false)} />
    </>
  );
};
