import { Button, Modal, TextInput, Label } from 'flowbite-react';
import { useState } from 'react';
import { toast } from 'react-toastify';

export const AddAccountModal = ({ isOpen, onClose }) => {
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Generate a realistic UUID for p_id
  const generateUUID = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = (Math.random() * 16) | 0;
      const v = c == 'x' ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!email || !name) {
      toast.error('Please fill in all fields');
      return;
    }

    setIsSubmitting(true);

    try {
      // Generate UUID for the application
      const pId = generateUUID();

      // Construct the Miko page URL
      const mikoUrl = `https://ng-account-fe-dev.dev1.ngnair.com/accounts/new?p_id=${pId}`;

      // Store the form data in localStorage for potential use
      localStorage.setItem(
        'pendingAccountApplication',
        JSON.stringify({
          email,
          name,
          pId,
          timestamp: new Date().toISOString(),
        }),
      );

      // Open the Miko page in a new tab
      window.open(mikoUrl, '_blank');

      toast.success('Redirecting to account application page...');

      // Reset form and close modal
      setEmail('');
      setName('');
      onClose();
    } catch (error) {
      toast.error('Failed to open application page');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setEmail('');
    setName('');
    onClose();
  };

  return (
    <Modal show={isOpen} onClose={handleClose} size="md">
      <Modal.Header>
        <h3 className="text-xl font-semibold text-gray-900">Add New Account</h3>
      </Modal.Header>

      <Modal.Body>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="name" value="Full Name" />
            <TextInput
              id="name"
              type="text"
              placeholder="Enter full name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
            />
          </div>

          <div>
            <Label htmlFor="email" value="Email Address" />
            <TextInput
              id="email"
              type="email"
              placeholder="Enter email address"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>
        </form>
      </Modal.Body>

      <Modal.Footer>
        <div className="flex w-full justify-between">
          <Button color="gray" onClick={handleClose}>
            Cancel
          </Button>
          <Button color="blue" onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? 'Opening Application...' : 'Continue to Application'}
          </Button>
        </div>
      </Modal.Footer>
    </Modal>
  );
};
