import { Processor } from '@/types/processors';

export const PAYMENT_PROCESSORS: Processor[] = [
  {
    id: 'globalpay_propay',
    name: 'GlobalPay - ProPay',
    type: 'PAYMENT',
    logo: '/icons/processors/globalpay.png',
    enabled: false,
    requiresConfig: true,
    configFields: {
      clientId: true,
      clientSecret: true,
      apiKey: true,
    },
  },
  {
    id: 'globalpay_tsys',
    name: 'GlobalPay - Transit (TSYS)',
    type: 'PAYMENT',
    logo: '/icons/processors/globalpay.png',
    enabled: false,
    requiresConfig: true,
    configFields: {
      clientId: true,
      clientSecret: true,
      apiKey: true,
    },
  },
  {
    id: 'elavon',
    name: 'Elavon',
    type: 'PAYMENT',
    logo: '/icons/processors/elavon.png',
    enabled: false,
    requiresConfig: true,
    configFields: {
      clientId: true,
      clientSecret: true,
    },
  },
  {
    id: 'cardconnect',
    name: 'CardConnect (Fiserv)',
    type: 'PAYMENT',
    logo: '/icons/processors/cardconnect.png',
    enabled: false,
    requiresConfig: true,
    configFields: {
      clientId: true,
      clientSecret: true,
    },
  },
  {
    id: 'worldpay',
    name: 'Worldpay',
    type: 'PAYMENT',
    logo: '/icons/processors/worldpay.png',
    enabled: false,
    requiresConfig: true,
    configFields: {
      clientId: true,
      clientSecret: true,
      apiKey: true,
    },
  },
];

export const CRM_PROCESSORS: Processor[] = [
  {
    id: 'gohighlevel',
    name: 'GoHighLevel',
    type: 'CRM',
    logo: '/icons/crm/gohighlevel.png',
    enabled: false,
    requiresConfig: true,
    configFields: {
      clientId: true,
      clientSecret: true,
    },
  },
  {
    id: 'hubspot',
    name: 'HubSpot',
    type: 'CRM',
    logo: '/icons/crm/hubspot.png',
    enabled: false,
    requiresConfig: true,
    configFields: {
      apiKey: true,
    },
  },
  {
    id: 'salesforce',
    name: 'Salesforce',
    type: 'CRM',
    logo: '/icons/crm/salesforce.png',
    enabled: false,
    requiresConfig: true,
    configFields: {
      clientId: true,
      clientSecret: true,
    },
  },
  {
    id: 'zoho',
    name: 'Zoho',
    type: 'CRM',
    logo: '/icons/crm/zoho.png',
    enabled: false,
    requiresConfig: true,
    configFields: {
      clientId: true,
      clientSecret: true,
    },
  },
  {
    id: 'zendesk',
    name: 'Zendesk',
    type: 'CRM',
    logo: '/icons/crm/zendesk.png',
    enabled: false,
    requiresConfig: true,
    configFields: {
      apiKey: true,
    },
  },
  {
    id: 'pipedrive',
    name: 'Pipedrive',
    type: 'CRM',
    logo: '/icons/crm/pipedrive.png',
    enabled: false,
    requiresConfig: true,
    configFields: {
      apiKey: true,
    },
  },
];

export const POS_PROCESSORS: Processor[] = [
  {
    id: 'square',
    name: 'Square',
    type: 'POS',
    logo: '/icons/pos/square.png',
    enabled: false,
    requiresConfig: true,
    configFields: {
      clientId: true,
      clientSecret: true,
    },
  },
  {
    id: 'clover',
    name: 'Clover',
    type: 'POS',
    logo: '/icons/pos/clover.png',
    enabled: false,
    requiresConfig: true,
    configFields: {
      apiKey: true,
    },
  },
  {
    id: 'toast',
    name: 'Toast',
    type: 'POS',
    logo: '/icons/pos/toast.png',
    enabled: false,
    requiresConfig: true,
    configFields: {
      clientId: true,
      clientSecret: true,
    },
  },
  {
    id: 'skytab',
    name: 'SkyTab',
    type: 'POS',
    logo: '/icons/pos/skytab.png',
    enabled: false,
    requiresConfig: true,
    configFields: {
      apiKey: true,
    },
  },
  {
    id: 'lightspeed',
    name: 'Lightspeed',
    type: 'POS',
    logo: '/icons/pos/lightspeed.png',
    enabled: false,
    requiresConfig: true,
    configFields: {
      clientId: true,
      clientSecret: true,
    },
  },
  {
    id: 'swiftpos',
    name: 'SwiftPOS',
    type: 'POS',
    logo: '/icons/pos/swiftpos.png',
    enabled: false,
    requiresConfig: true,
    configFields: {
      apiKey: true,
    },
  },
];
